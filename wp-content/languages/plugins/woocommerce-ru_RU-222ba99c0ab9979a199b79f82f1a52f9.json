{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "The quantity of \"%1$s\" was changed to %2$d.": ["Количество товара \"%1$s\" было изменено на %2$d."], "\"%s\" was removed from your cart.": ["Товар \"%s\" был удален из вашей корзины."], "Flat rate shipping": ["Доставка по единой ставке"], "T-Shirt": ["Футболка"], "Hoodie with Pocket": ["Худи с карманом"], "Hoodie with Logo": ["Худи с логотипом"], "Hoodie with Zipper": ["Худи с молнией"], "Long Sleeve Tee": ["Лонгслив"], "Polo": ["Поло"], "%s (optional)": ["%s (необязательно)"], "There was an error registering the payment method with id '%s': ": ["При регистрации метода оплаты с помощью ID «%s» произошла ошибка: "], "Orange": ["Оранжевый"], "Lightweight baseball cap": ["Лёгкая бейсболка"], "Cap": ["Кепка"], "Yellow": ["Жёлтая"], "Warm hat for winter": ["Тёплая зимняя шапка"], "Beanie": ["Вязаная шапка"], "example product in Cart Block\u0004Beanie": ["Вязаная шапка"], "example product in Cart Block\u0004Beanie with Logo": ["Вязаная шапка с логотипом"], "Something went wrong. Please contact us to get assistance.": ["Что-то сломалось. Свяжитесь с нами, если нужна помощь."], "Unable to get cart data from the API.": ["Не удалось получить данные корзины из API."], "The response is not a valid JSON response.": ["Ответ не является действительным ответом JSON."], "Sales tax": ["Налог с продаж"], "Color": ["Цвет"], "Small": ["Маленький"], "Size": ["Размер"], "Free shipping": ["Бесплатная доставка"], "Shipping": ["Доставка"], "Local pickup": ["Самовывоз"], "Fee": ["Комиссия"]}}, "comment": {"reference": "assets/client/blocks/wc-blocks-data.js"}}