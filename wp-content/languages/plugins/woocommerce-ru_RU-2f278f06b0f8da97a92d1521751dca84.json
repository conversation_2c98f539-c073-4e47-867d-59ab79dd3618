{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "Revenue is now reported from paid orders ✅": ["Теперь данные о доходах не содержат неоплаченные заказы ✅"], "Analytics date settings": ["Настройки аналитики"], "We now collect orders in this table based on when the payment went through, rather than when they were placed. You can change this in <link>settings</link>.": ["Теперь мы собираем заказы в этой таблице на основе того, когда был произведен платёж, а не когда они были размещены. Это можно изменить в <link>настройках</link>."], "Full refunds are not deducted from tax or net sales totals": ["Полный возврат не вычитается из суммы налога или чистой выручки"], "All Revenue": ["Весь доход"], "A sentence describing filters for Revenue. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ\u0004Revenue Matches <select/> Filters": ["Доход соответствует фильтрам <select/>"], "Got it": ["Ясно"], "day": ["день", "дня", "<PERSON><PERSON><PERSON><PERSON>"], "Gross sales": ["Валовый доход"], "order": ["зак<PERSON>з", "заказа", "<PERSON>а<PERSON><PERSON><PERSON><PERSON>"], "Advanced Filters": ["Расширенные фильтры"], "Returns": ["Возвраты"], "Net sales": ["Чистая выручка"], "Revenue": ["Доход"], "Total sales": ["Всего продаж"], "Show": ["Показать"], "Shipping": ["Доставка"], "Taxes": ["Налоги"], "Orders": ["Заказы"], "Date": ["Дата"], "Coupons": ["Купоны"]}}, "comment": {"reference": "assets/client/admin/chunks/analytics-report-revenue.js"}}