{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "Related Products": ["Связанные товары"], "Displays the PHP product search results.": ["Отображает результаты поиска товара на PHP."], "Product Search Results (Classic)": ["Результаты поиска товара (классика)"], "Displays the PHP product's custom taxonomy page.": ["Отображ<PERSON><PERSON>т страницу произвольной таксономии товара на PHP."], "Product's Custom Taxonomy (Classic)": ["Произвольная таксономия товара (классика)"], "Displays the PHP product attribute page.": ["Отобра<PERSON><PERSON><PERSON>т страницу с атрибутом товара на PHP."], "Product Attribute (Classic)": ["Атрибут товара (классика)"], "Displays the PHP product tag page.": ["Отобра<PERSON><PERSON><PERSON>т страницу с меткой товара на PHP."], "Product Tag (Classic)": ["Метка товара (классика)"], "Displays the PHP product category page.": ["Отобра<PERSON><PERSON><PERSON>т страницу категории товаров на PHP."], "Product Category (Classic)": ["Категория товаров (классика)"], "Displays the PHP product grid page. ": ["Отобра<PERSON><PERSON><PERSON>т страницу с сеткой товаров на PHP. "], "Product Grid (Classic)": ["Сетка товаров (классика)"], "Displays the PHP product page.": ["Отобр<PERSON><PERSON><PERSON><PERSON>т страницу товара на PHP."], "Product (Classic)": ["Товар (классика)"], "You cannot edit the content of this block. However, you can move it and place other blocks around it.": ["Вы не можете редактировать содержимое этого блока. Однако, вы можете переместить его и разместить вокруг него другие блоки."], "Classic Template Placeholder": ["Классический шаблон-заполнитель"], "This block represents the classic template used to display the order confirmation. The actual rendered template may appear different from this placeholder.": ["Этот блок представляет собой классический шаблон, используемый для подтверждения заказа. Фактический отрисованный шаблон может отличаться от этого заполнителя."], "Order Confirmation Block": ["Блок подтверждения заказа"], "Checkout Header": ["Заголовок оформления заказа"], "Template transformed into blocks!": ["Шаблон преобразован в блоки!"], "Transform into blocks": ["Трансформировать в блоки"], "Transform this template into multiple blocks so you can add, remove, reorder, and customize your %s template.": ["Преобразование этого шаблона в несколько блоков, что позволяет добавлять, удалять, изменять порядок и настраивать шаблон %s."], "This block serves as a placeholder for your %s. It will display the actual product image, title, price in your store. You can move this placeholder around and add more blocks around to customize the template.": ["Блок служит заполнителем для %s. В нём будет отображено изображение товара, название, цена в вашем магазине. Можно перемещать этот заполнитель и добавлять дополнительные блоки, чтобы настроить шаблон."], "WooCommerce Classic Template": ["Классический шаблон WooCommerce"], "Renders classic WooCommerce PHP templates.": ["Отображ<PERSON>ет классические PHP-шаблоны WooCommerce."], "Undo": ["Отменить"], "Order number": ["Номер заказа"], "Display the title of a product.": ["Отображать наименование товара."], "Search products…": ["Искать товары…"], "Product Title": ["Заголовок товара"], "Order details": ["Информация о заказе"], "No products were found matching your selection.": ["Товаров, соответствующих вашему запросу, не обнаружено."], "Shipping address": ["Адрес доставки"], "Billing address": ["Платёжный адрес"], "Thank you. Your order has been received.": ["Ваш заказ принят. Благодарим вас."], "Subtotal": ["Подытог"], "Order received": ["Заказ принят"], "Email": ["Email"], "Search": ["Поиск"], "Total": ["Итого"], "Date": ["Дата"], "Product": ["<PERSON><PERSON><PERSON><PERSON>", "Товара", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "WooCommerce": ["WooCommerce"]}}, "comment": {"reference": "assets/client/blocks/legacy-template.js"}}