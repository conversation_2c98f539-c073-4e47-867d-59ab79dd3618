{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "Max. Price": ["Макс. цена"], "Min. Price": ["Мин. цена"], "Filter block: We have improved this block to make styling easier. Upgrade it using the button below.": ["Блок фильтров: мы улучшили этот блок, чтобы упростить управление стилем. Обновите его, нажав кнопку ниже."], "Upgrade block": ["Обновить блок"], "To filter your products by price you first need to assign prices to your products.": ["Для фильтрации товаров по цене необходимо назначить цены товарам."], "Show 'Apply filters' button": ["Отображение кнопки «Применить фильтры»"], "Show input fields inline with the slider.": ["Отображение поля ввода со слайдером."], "Inline input fields": ["Встроенные поля ввода"], "Price Range Selector": ["Выбор ценового диапазона"], "Filter by Price": ["Фильтрация по цене"], "Reset price filter": ["Сброс ценового фильтра"], "Reset filter": ["Сброс фильтра"], "Block title": ["Заголовок блока"], "Apply price filter": ["Применить фильтр цен"], "Apply filter": ["Применить фильтр"], "Products will update when the button is clicked.": ["Товары будут обновляться только по нажатию кнопки."], "Editable": ["Редактируемое значение"], "Filter products by maximum price": ["Сортировать по цене (сначала дорогие)"], "Filter products by minimum price": ["Сортировать по цене (сначала дешевые)"], "Display a slider to filter products in your store by price.": ["Отображать слайдер для фильтрации товаров по цене."], "Filter by price": ["Фильтрация по цене"], "Text": ["Текст"], "Add new product": ["Добавить товар"], "Reset": ["Сброс"], "Learn more": ["Узнать больше"], "Apply": ["Применить"], "Settings": ["Настройки"]}}, "comment": {"reference": "assets/client/blocks/price-filter.js"}}