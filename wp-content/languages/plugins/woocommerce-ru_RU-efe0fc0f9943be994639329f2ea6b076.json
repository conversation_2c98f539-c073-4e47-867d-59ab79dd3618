{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "Weight must be greater than zero.": ["Вес должен быть больше нуля."], "Height must be greater than zero.": ["Высота должна быть больше нуля."], "Length must be greater than zero.": ["Длина должна быть больше нуля."], "Width must be greater than zero.": ["Ширина должна быть больше нуля."], "Height <Side />": ["Высота <Side />"], "Length <Side />": ["<PERSON><PERSON><PERSON><PERSON> <Side />"], "Width <Side />": ["<PERSON><PERSON><PERSON><PERSON><PERSON> <Side />"], "Dimensions": ["Га<PERSON><PERSON><PERSON><PERSON><PERSON>ы"], "Weight": ["<PERSON>е<PERSON>"]}}, "comment": {"reference": "assets/client/admin/product-editor/blocks/product-fields/shipping-dimensions/edit.js"}}