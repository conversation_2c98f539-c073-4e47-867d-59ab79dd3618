{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "Upgrade to the blockified Add to Cart with Options block": ["Расширить функционал добавлению в корзину с опциями"], "a new blockified experience": ["новый опыт блочности"], "Upgrade the Add to Cart with Options block to <strongText /> for more features!": ["Расширить функционал блока добавления в корзину с опциями до <strongText /> для получения дополнительных возможностей!"], "Stepper": ["Серия шагов"], "Input": ["Ввод"], "Quantity Selector": ["Выбор количества"], "Shoppers can use buttons to change the number of items to add to cart.": ["Покупатели могут добавлять нужное количество товаров в корзину при помощи кнопок."], "Shoppers can enter a number of items to add to cart.": ["Покупатели могут добавлять нужное количество товаров в корзину, вводя число."], "Customer will see product add-to-cart options in this space, dependent on the product type.": ["Клиенты увидят здесь опции добавления товара в корзину, отображаемые в зависимости от типа товара."], "Add to cart": ["В корзину"]}}, "comment": {"reference": "assets/client/blocks/add-to-cart-form.js"}}