{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "This will affect product category pages": ["Это повлияет на страницы категорий товаров"], "Only show children of current category": ["Отображение только дочерних элементов текущей категории"], "Category images are hidden.": ["Изображения категорий скрыты."], "Category images are visible.": ["Изображения категорий видны."], "Show category images": ["Показывать изображения категорий"], "This block displays the product categories for your store. To use it you first need to create a product and assign it to a category.": ["Этот блок отображает категории товаров вашего магазина. Для начала работы с ним необходимо создать товар и назначить ему категорию."], "Display style": ["Стиль отображения"], "List Settings": ["Настройки списка"], "Show empty categories": ["Отображать пустые категории"], "Show product count": ["Отоб<PERSON><PERSON><PERSON><PERSON><PERSON>ь счётчик товаров"], "Product Categories List": ["Список категорий товаров"], "Show hierarchy": ["Показывать иерархию"], "Dropdown": ["Выпадающее меню"], "List": ["Список"], "Content": ["Содержание"]}}, "comment": {"reference": "assets/client/blocks/product-categories.js"}}