{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "Go to the Customizer": ["Перейти в конфигуратор"], "Customize everything from the color palette and the fonts to the page layouts, making sure every detail aligns with your brand.": ["Настройте по вашему вкусу всё, от цветовой гаммы до шрифтов и макетов страниц. Пусть каждая деталь соответствует эстетике вашего бренда."], "Design a new theme": ["Создать новую тему"], "Your active theme will be changed and you could lose any changes you’ve made to it.": ["Активная тема сайта будет изменена, и текущие настройки темы могут быть утрачены."], "Are you sure you want to design a new theme?": ["Уверены, что хотите создать новую тему?"], "Customize your theme": ["Настройте свою тему"], "Start designing": ["Дизайн: начало"], "Quickly create a beautiful store using our built-in store designer. Choose your layout, select a style, and much more.": ["С помощью встроенного дизайнера магазина вы можете быстро создать красивый онлайн-магазин. Выберите макет, стиль и всё остальное."], "Design your own": ["Создайте свой дизайн"], "Powered by experimental AI. {{link}}Learn more{{/link}}": ["Работает на экспериментальном ИИ. {{link}}Подробности{{/link}}"], "Start again": ["Начать снова"], "You'll be asked to provide your business info again, and will lose your existing AI design. If you want to customize your existing design, you can do so via the <EditorLink>Editor</EditorLink>.": ["Вам снова надо будет предоставить информацию о своей компании, и вы потеряете существующий дизайн ИИ. Если желаете самостоятельно настроить существующий дизайн, можете сделать это с помощью <EditorLink>редактора</EditorLink>."], "Are you sure you want to start over?": ["Уверены, что хотите начать всё сначала?"], "The Store Designer will create a new store design for you, and you'll lose any changes you've made to your active theme. If you'd prefer to continue editing your theme, you can do so via the <EditorLink>Editor</EditorLink>.": ["Компоновщик магазина создаст для вас новый дизайн магазина, и вы потеряете все изменения, внесённые в активную тему сайта. Если вы предпочитаете продолжить редактирование своей темы, вы можете сделать это с помощью <EditorLink>редактора</EditorLink>."], "Are you sure you want to start a new design?": ["Уверены, что хотите начать создавать новый дизайн?"], "Create a new one": ["Создать новый"], "Keep customizing the look of your AI-generated store, or start over and create a new one.": ["Продолжайте настройку внешнего вида своего магазина, созданного с помощью ИИ, или начните новую."], "Customize your custom theme": ["Настройте свою уникальную тему"], "Find out how": ["Узнать как"], "It looks like you're using Jetpack's offline mode — switch to online mode to start designing with AI.": ["Похоже, вы используете автономный режим Jetpack. Переключитесь в онлайн-режим, чтобы начать проектирование с помощью ИИ."], "Unfortunately, the [AI Store designer] isn't available right now as we can't detect your network. Please check your internet connection.": ["К сожалению, [Компоновщик магазина ИИ] сейчас недоступен, так как мы не можем определить вашу сеть. Проверьте своё подключение к Интернету."], "Looking to design your store using AI?": ["Хотите спроектировать свой магазин с использованием ИИ?"], "Go to the Editor": ["Перейти в редактор"], "Design with AI": ["Настройка с поддержкой ИИ"], "Design the look of your store, create pages, and generate copy using our built-in AI tools.": ["Разрабатывайте дизайн вашего магазина, создавайте страницы и текст с помощью встроенных инструментов на базе ИИ."], "Use the power of AI to design your store": ["Создавайте дизайн магазина, пользуясь богатыми возможностями ИИ"], "Customize your store": ["Настройка параметров магазина"], "Customize": ["Настроить"], "Cancel": ["Отмена"]}}, "comment": {"reference": "assets/client/admin/chunks/6916.js"}}