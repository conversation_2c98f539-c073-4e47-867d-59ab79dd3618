{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "Visit the {{Link}}Official WooCommerce Marketplace{{/Link}} to find more shipping, delivery, and fulfillment solutions.": ["Посетите {{Link}}официальный магазин WooCommerce Marketplace{{/Link}}, чтобы найти новые решения в сфере перевозок, доставки и выполнения заказов."], "For example, we’ve added the “Free shipping” method for shoppers in your country. You can edit, add to, or remove shipping methods by selecting Edit or Delete.": ["Например, мы установили опцию «Бесплатная доставка» для покупателей из вашей страны. Можно редактировать, добавлять или удалять способы доставки, выбрав «Редактировать» или «Удалить»."], "Add one or more shipping methods you’d like to offer to shoppers in your zones.": ["Добавьте один или несколько способов доставки для покупателей из различных зон."], "We’ve added some shipping zones to get you started — you can manage them by selecting Edit or Delete.": ["Для начала мы добавили несколько зон доставки: вы можете управлять ими, выбрав «Редактировать» или «Удалить»."], "Specify the areas you’d like to ship to! Give each zone a name, then list the regions you’d like to include. Your regions can be as specific as a zip code or as broad as a country. Shoppers will only see the methods available in their region.": ["Выберите регионы, куда вы собираетесь доставлять товары. Дайте название каждой из зон, а затем перечислите регионы, которые хотите включить в неё. Регионы можно определять по-разному: от зоны действия одного почтового индекса до целой страны. Покупатели увидят только способы доставки, доступные в их регионе."], "There was a problem marking the shipping tour as completed.": ["Возникла проблема при отметке доставки как выполненной."], "If you’d like to speed up your process and print your shipping label straight from your Woo dashboard, WooCommerce Shipping may be for you! ": ["Если нужно ускорить процесс и печатать этикетки для доставки прямо из панели управления Woo, WooCommerce Shipping вам поможет! "], "Print USPS, UPS, and DHL labels straight from your Woo dashboard and save on shipping thanks to discounted rates. You can manage WooCommerce Shipping in this section.": ["Печатайте этикетки USPS, UPS и DHL прямо из панели управления Woo и экономьте на доставке благодаря скидкам. В этом разделе вы можете управлять доставкой WooCommerce."], "We recommend adding one of the following shipping extensions to your store. The extension will be installed and activated for you when you click \"Get started\".": ["Рекомендуем добавить одно из следующих расширений оплаты в ваш магазин. Расширение будет установлено и активировано для вас при нажатии «Приступить»."], "Recommended shipping solutions": ["Рекомендуемые решения для доставки"], "🎉 WooCommerce Shipping is installed!": ["🎉 WooCommerce Shipping установлен!"], "Finish the setup by connecting your store to WordPress.com.": ["Завершите настройку, подключив магазин к WordPress.com."], "Print USPS, UPS, and DHL Express labels straight from your WooCommerce dashboard and save on shipping.": ["Печатайте этикетки USPS, UPS и DHL Express прямо из консоли WooCommerce и экономьте на доставке."], "WooCommerce Shipping": ["Доставка WooCommerce"], "Hide this": ["Скрыть это"], "Get started": ["Приступить"], "Task List Options": ["Параметры списка задач"], "Recommended": ["Рекомендуется"], "Activate": ["Активир<PERSON>ать"], "Shipping zones": ["Зоны доставки"], "Shipping methods": ["Методы доставки"], "Learn more": ["Узнать больше"]}}, "comment": {"reference": "assets/client/admin/chunks/shipping-recommendations.js"}}