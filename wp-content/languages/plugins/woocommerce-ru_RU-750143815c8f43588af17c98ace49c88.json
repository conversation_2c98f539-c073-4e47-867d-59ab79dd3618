{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "Toggle to disable opening the Mini-Cart drawer when clicking the cart icon, and instead navigate to the checkout page.": ["Переключите, чтобы при нажатии на значок корзины вместо открытия ящика мини-корзины происходил переход на страницу оформления заказа."], "Navigate to checkout when clicking the Mini-Cart, instead of opening the drawer.": ["При нажатии на мини-корзину вместо открытия ящика переходить к оформлению заказа."], "The editor does not display the real count value, but a placeholder to indicate how it will look on the front-end.": ["Редактор отображает не реальное значение счетчика, а заполнитель, показывающий, как оно будет выглядеть во внешнем интерфейсе."], "Only if cart has items": ["Только если в корзине есть товары"], "Always (even if empty)": ["Всегда (даже при пустой корзине)"], "Show Cart Item Count:": ["Показывать количество позиций в корзине:"], "Product Count": ["Счётчик товаров"], "Cart Icon": ["Значок корзины"], "Icon": ["Значок"], "Toggle to open the Mini-Cart drawer when a shopper adds a product to their cart.": ["Отображение выезжающей панели мини-корзины, когда покупатель добавляет товар в свою корзину."], "Open drawer when adding": ["Открытие панели при добавлении"], "Behavior": ["Поведение"], "Edit Mini-Cart Drawer template": ["Правка шаблона выезжающей панели мини-корзины"], "When opened, the Mini-Cart drawer gives shoppers quick access to view their selected products and checkout.": ["При открытии выезжающая панель мини-корзины предоставляет покупателям быстрый доступ к выбранным товарам и оформлению заказа."], "Cart Drawer": ["Выезжающая панель корзины"], "Toggle to display the total price of products in the shopping cart. If no products have been added, the price will not display.": ["Отображение итоговой цены товаров в корзине. Если корзина пуста, цена отображаться не будет."], "Display total price": ["Отображение итоговой цены"], "Select how the Mini-Cart behaves in the Cart and Checkout pages. This might affect the header layout.": ["Поведение мини-корзины на страницах корзины и оформления заказа. Может влиять на макет заголовка."], "Mini-Cart in cart and checkout pages": ["Мини-корзина на страницах корзины и оформления заказа"], "Hide": ["Скрыть"], "Display": ["Отображаются"], "Never": ["Никогда"], "Price": ["Цена"], "Remove": ["Удалить"], "Settings": ["Настройки"]}}, "comment": {"reference": "assets/client/blocks/mini-cart.js"}}