{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "You can learn more about the benefits of switching to blocks, compatibility with extensions, and how to switch back to shortcodes <a>in our documentation</a>.": ["Подробности о преимуществах перехода на блоки, о совместимости с расширениями и о том, как вернуться к шорткодам ищите в <a>нашей документации</a>."], "Classic Shortcode Placeholder": ["Заполнитель классического шорткода"], "Classic shortcode transformed to blocks.": ["Классический шорткод преобразован в блок."], "This block will render the classic cart shortcode. You can optionally transform it into blocks for more control over the cart experience.": ["Этот блок отобразит классический шорткод корзины. При необходимости, его можно преобразовать в блоки для большего контроля над работой корзины."], "Classic Cart": ["Классическая корзина"], "This block will render the classic checkout shortcode. You can optionally transform it into blocks for more control over the checkout experience.": ["Данный блок отобразит классический шорткод оформления заказа. При необходимости, его можно преобразовать в блоки для большего контроля над процессом оформления заказа."], "Classic Checkout": ["Классическое оформление заказа"], "Renders the classic checkout shortcode.": ["Отображ<PERSON><PERSON>т классический шорткод оформления заказа."], "Checkout Cart": ["Корзина оформления заказа"], "Renders the classic cart shortcode.": ["Отрисовка классического шорткода корзины."], "Cart Shortcode": ["Шорткод корзины"], "Transform into blocks": ["Трансформировать в блоки"], "Undo": ["Отменить"], "Learn more": ["Узнать больше"], "WooCommerce": ["WooCommerce"]}}, "comment": {"reference": "assets/client/blocks/classic-shortcode.js"}}