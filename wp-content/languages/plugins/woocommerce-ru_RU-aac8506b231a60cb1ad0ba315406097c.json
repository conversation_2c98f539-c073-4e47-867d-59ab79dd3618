{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "Toggle block inserter": ["Включить инструмент вставки блоков"], "List View": ["В виде списка"], "Show or hide the Settings sidebar.": ["Показать или скрыть боковую панель «Настройки»."], "Open the block list view.": ["Открыть вид списка блоков."], "Redo your last undo.": ["Повторить последнюю отмену."], "Undo your last changes.": ["Отменить последние изменения."], "Plugins": ["Плагины"], "Hide block tools": ["Скрыть инструменты блоков"], "Show block tools": ["Показать инструменты блоков"], "Top toolbar deactivated": ["Верхняя панель инструментов деактивирована"], "Top toolbar activated": ["Верхняя панель инструментов активирована"], "Access all block and document tools in a single place": ["Доступ ко всем блокам и инструментам в одном месте"], "Top toolbar": ["Верхняя панель инструментов"], "https://wordpress.org/documentation/article/wordpress-block-editor/": ["https://wordpress.org/documentation/article/wordpress-block-editor/"], "Copy all content": ["Скопировать всё содержимое"], "All content copied.": ["Всё содержимое было скопировано."], "Document overview": ["Обзор документа"], "Document tools": ["Инструменты документа"], "Redo": ["Вернуть"], "Use left and right arrow keys to resize the canvas.": ["Чтобы изменить размер холста, используйте клавиши со стрелками влево и вправо."], "Drag to resize": ["Перетащите, чтобы изменить размер"], "Back": ["Назад"], "Help": ["Помощь"], "Close settings": ["Закрыть настройки"], "Undo": ["Отменить"], "(opens in a new tab)": ["(откроется в новой вкладке)"], "Done": ["Готово"], "Tools": ["Инструменты"], "Close": ["Закрыть"], "Cancel": ["Отмена"], "View": ["Просмотр"], "Settings": ["Настройки"]}}, "comment": {"reference": "assets/client/admin/chunks/8499.js"}}