{"translation-revision-date": "2025-05-23 07:11:16+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "Offset": ["Смещение"], "Product ID": ["ID товара"], "Product Title": ["Заголовок товара"], "Popular": ["Популярные"], "Rating": ["<PERSON>ей<PERSON>инг"], "Products Per Page": ["Товаров на странице"], "Ascending": ["По возрастанию"], "Descending": ["По убыванию"], "Product Slug": ["Ярлык товара"], "Product Tags": ["Метки товара"], "Product Categories": ["Категории товаров"], "ZoomInDown": ["Приближение вниз"], "ZoomInLeft": ["Приближение влево"], "ZoomInRight": ["Приближение вправо"], "ZoomInUp": ["Приближение вверх"], "Flip": ["Переворот"], "FlipInX": ["Переворот по X"], "FlipInY": ["Переворот по Y"], "Animation": ["Анимация"], "Select Animation": ["Выбрать анимацию"], "Animation Delay": ["Задержка анимации"], "Custom CSS": ["Произвольные CSS-стили"], "Hide on Tab": ["Скрыть на вкладке"], "Jello": ["<PERSON><PERSON>"], "Hide on Desktop": ["Скрывать на компьютере"], "Responsive Control": ["Контроль адаптивности"], "RotateIn": ["Повернуть внутрь"], "Tada": ["<PERSON><PERSON>"], "Wobble": ["Колебание"], "FadeInDown": ["Появление снизу"], "FadeInRight": ["Появление справа"], "Flash": ["Flash"], "Bounce": ["Отскок"], "FadeIn": ["Постепенное появление"], "Swing": ["Колебание"], "RubberBand": ["RubberBand"], "SlideInUp": ["Прискольжение вверх"], "SlideInDown": ["Прискольжение вниз"], "SlideInLeft": ["Прискольжение влево"], "SlideInRight": ["Прискольжение вправо"], "ZoomIn": ["Приближение"], "FadeInLeft": ["Появление налево"], "FadeInUp": ["Появление вверх"], "BounceIn": ["Отскок внутрь"], "BounceInDown": ["Отскок внутрь вниз"], "BounceInUp": ["Отскок внутрь вверх"], "Italic": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Classic": ["Классический"], "Pagination": ["Разделение на страницы"], "Auto": ["Авто"], "Title": ["Заголовок"], "Overlay": ["Наложение"], "Cover": ["Обложка"], "Date": ["Дата"], "Underline": ["Подчёркнутый"], "Default": ["По умолчанию"], "Color": ["Цвет"], "Gradient": ["Град<PERSON><PERSON><PERSON><PERSON>"], "Top Center": ["Вверху по центру"], "Center Left": ["Слева по центру"], "Center Right": ["Справа по центру"], "Bottom Center": ["Внизу по центру"], "Bottom Right": ["Внизу справа"], "100": ["100"], "Uppercase": ["Верхний регистр"], "Lowercase": ["Нижний регистр"], "Capitalize": ["Заглавные буквы"], "Hover": ["При наведении"], "Fixed": ["Фиксировано"], "Upload Image": ["Загрузить изображение"], "Blend Mode": ["Режим наложения"], "Multiply": ["Умножение"], "Screen": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Darken": ["Затемнение"], "Lighten": ["Осветление"], "Saturation": ["Насыщенность"], "Luminosity": ["Яркость"], "CSS Filters": ["Фильтры CSS"], "Blur": ["Размытие"], "Brightness": ["Яркость"], "Contrast": ["Контраст"], "overlay": ["наложение"], "saturation": ["насыщение"], "color": ["цвет"], "luminosity": ["освещение"], "Background Type": ["Тип фона"], "Background Transition": ["Фоновый переход"], "Enable Overlay": ["Включить наложение"], "Border Style": ["Стиль рамки"], "Double": ["Двойная"], "Groove": ["Выпуклость"], "Inset": ["Вставка"], "Outset": ["Выпуклый"], "Ridge": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Shadow Color": ["Цвет тени"], "Horizontal Offset": ["Горизонтальное смещение"], "Vertical Offset": ["Вертикальное смещение"], "Shadow Blur": ["Размытие тени"], "Shadow Spread": ["Распространение тени"], "Alignment": ["Выравнивание"], "Icon Background": ["Фон значка"], "200": ["200"], "300": ["300"], "400": ["400"], "500": ["500"], "600": ["600"], "700": ["700"], "800": ["800"], "900": ["900"], "Overline": ["Надстрочный"], "Line Through": ["Перечёркнутый"], "Oblique": ["Наклон"], "Font Style": ["Стиль шрифта"], "Text Transform": ["Преобразование текста"], "Text Decoration": ["Оформление текста"], "Letter Spacing": ["Межбуквенный интервал"], "Open in New Tab": ["Открыть в новой вкладке"], "Price": ["Цена"], "Animation Speed": ["Скорость анимации"], "Background Overlay": ["Перекрытие фона"], "Overlay Type": ["Тип наложения"], "Overlay Color": ["Цвет наложения"], "Overlay Image": ["Наложение изображения"], "Asc": ["По возрастанию"], "Background Color": ["Цвет фона"], "Button Text": ["Текст кнопки"], "Font Size": ["Размер шрифта"], "Icon Color": ["Цвет значка"], "ID": ["ID"], "Parent": ["Родитель"], "Repeat": ["Повтор"], "Center Center": ["По центру"], "Desc": ["По убыванию"], "Font Weight": ["Тол<PERSON>ина шрифта"], "Background Image": ["Фоновое изображение"], "Font Family": ["Семейство шрифтов"], "Post": ["Запись"], "Border Color": ["Цвет рамки"], "Hover Shadow Color": ["Цвет тени при наведении"], "Scroll": ["Прокрутка"], "No-repeat": ["Не повторять"], "Repeat-x": ["Повтор горизонталь"], "Repeat-y": ["Повтор вертикаль"], "darken": ["темнеть"], "Enable More Posts Option?": ["Включить опцию Больше записей?"], "Hue": ["Оттенок"], "Opacity": ["Прозрачность"], "Overlay Transition": ["Переход наложения"], "Shadow Transition": ["Переход теней"], "Hide on Mobile": ["Скрывать на телефоне"], "Opacity Transition": ["Переход прозрачности"], "Modified Date": ["Дата изменения"], "Contain": ["Вместить"], "multiply": ["умножать"], "lighten": ["осветлить"], "Css Filters Transition": ["Переходные фильтры Css"], "Pulse": ["Пульс"], "Border Transition": ["Граница перехода"], "Color Dodge": ["Цветовое осветление"], "color-dodge": ["цветовое осветление"], "screen": ["экран"], "Bottom Left": ["Низ Лево"], "Box Shadow": ["Тень бокса"], "Custom": ["Пользоватьские"], "Dashed": ["Штриховой пунктир"], "Dotted": ["Точечный пунктир"], "Line Height": ["Высота линии"], "Load More Button": ["Кнопка \"Загрузить еще\""], "None": ["Отсутствует"], "Normal": ["Обычное"], "Solid": ["Сплошная"], "Top Left": ["Сверху слева"], "Top Right": ["Верх Право"], "Border Radius Transition": ["Переход скругления углов"], "Angle": ["Угол"], "Gradient Type": ["Тип градиента"], "Radial Type": ["Радиальный тип"], "First Color Position": ["Позиция первого цвета"], "Second Color Position": ["Позиция второго цвета"], "Center X Position": ["Центральная позиция по горизонтали"], "Center Y Position": ["Центральная позиция по вертикали"], "Query": ["Запрос"], "Source": ["Источник"], "Order By": ["Упорядочить по"], "Order": ["Упорядочение"]}}, "comment": {"reference": "dist/controls.js"}}