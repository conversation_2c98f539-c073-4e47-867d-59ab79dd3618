{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "Pickup": ["Подобрать"], "Admin settings\u0004General": ["Основные"], "By enabling Local Pickup with more valuable features for your store, it's recommended that you remove the legacy Local Pickup option from your <a>shipping zones</a>.": ["Включив самовывоз с полезным функционалом магазина, рекомендуется удалить устаревшую опцию локальной доставки из <a>зон доставки</a>."], "If local pickup is enabled, the \"Hide shipping costs until an address is entered\" setting will be ignored.": ["Если включена опция самовывоза, параметр «Скрывать стоимость доставки до ввода адреса» будет игнорироваться."], "Add pickup location": ["Добавить место выдачи"], "When you add a pickup location, it will appear here.": ["Когда вы добавите место выдачи, оно появится здесь."], "Define pickup locations for your customers to choose from during checkout.": ["Определите места выдачи самовывоза, чтобы ваши клиенты могли выбирать из них во время оформления заказа."], "Pickup locations": ["Место выдачи"], "Delete location": ["Удалить место"], "Edit pickup location": ["Изменить место самовывоза"], "Pickup details": ["Детали самовывоза"], "A Location title is required": ["Требуется название места"], "Location name": ["Название места"], "Not taxable": ["Не облагается налогом"], "By default, the local pickup shipping method is free.": ["По умолчанию способ доставки самовывозом является бесплатным."], "Add a price for customers who choose local pickup": ["Добавьте цену для клиентов, которые выбирают самовывоз"], "Local pickup title is required": ["Требуется название местного самовывоза"], "This is the shipping method title shown to customers.": ["Это название способа доставки, показываемое клиентам."], "When enabled, local pickup will appear as an option on the block based checkout.": ["При включении вариант самовывоза появится в качестве опции при оформлении заказа на основе блоков."], "View checkout page": ["Просмотреть страницу оформления заказа"], "Enable or disable local pickup on your store, and define costs. Local pickup is only available from the block checkout.": ["Включите или отключите самовывоз в своём магазине и определите его стоимость. Самовывоз доступен только из блочного оформления заказа."], "Local Pickup settings have been saved.": ["Настройки самовывоза были сохранены."], "Optional cost to charge for local pickup.": ["Дополнительная плата за самовывоз."], "If a cost is defined, this controls if taxes are applied to that cost.": ["Если стоимость определена, эта опция определяет, применяются ли налоги к той сумме."], "Pickup location": ["Место выдачи"], "Done": ["Готово"], "Country / State": ["Страна/регион"], "Enable local pickup": ["Включить самовывоз"], "Title": ["Наименование"], "Postcode / ZIP": ["Почтовый индекс"], "Save changes": ["Сохранить изменения"], "Free": ["Бесплатно"], "Enabled": ["Включить"], "Cost": ["Стоимость"], "Learn more": ["Узнать больше"], "Taxable": ["Налогооблагаемый"], "Address": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Адреса", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "State": ["Область"], "Taxes": ["Налоги"], "City": ["Город"], "Cancel": ["Отмена"], "Edit": ["Изменить"]}}, "comment": {"reference": "assets/client/blocks/wc-shipping-method-pickup-location.js"}}