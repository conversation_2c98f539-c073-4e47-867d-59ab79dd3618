{"translation-revision-date": "2025-05-24 12:05:47+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);", "lang": "ru"}, "deleteReview": ["deleteReview"], "updateReview": ["updateReview"], "A plugin was successfully installed and activated.": ["Плагин успешно установлен и активирован."], "%1$s (%2$s) was successfully installed and activated.": ["%1$s (%2$s) успешно установлен и активирован."], "Google for WooCommerce": ["Google for WooCommerce"], "WooPayments": ["WooPayments"], "Omnichannel for WooCommerce": ["Многоканальность WooCommerce"], "TikTok for WooCommerce": ["TikTok для WooCommerce"], "Pinterest for WooCommerce": ["Pinterest для WooCommerce"], "Mercado Pago payments for WooCommerce": ["Платежи Mercado Pago для WooCommerce"], "MailPoet": ["MailPoet"], "Could not %(actionType)s %(pluginName)s plugin, %(error)s": ["Не удалось %(actionType)s плагин %(pluginName)s, %(error)s", "Не удалось %(actionType)s плагины: %(pluginName)s, возникли ошибки: %(error)s", "Не удалось %(actionType)s плагины: %(pluginName)s, возникли ошибки: %(error)s"], "Razorpay": ["Razorpay"], "Creative Mail for WooCommerce": ["Creative Mail для WooCommerce"], "WooCommerce Shipping & Tax": ["WooCommerce доставка и налоги"], "WooCommerce Payfast": ["WooCommerce Payfast"], "WooCommerce Stripe": ["WooCommerce Stripe"], "WooCommerce PayPal": ["WooCommerce PayPal"], "WooCommerce ShipStation Gateway": ["WooCommerce ShipStation Gateway"], "There was a problem updating your settings.": ["При обновлении настроек возникла проблема."], "Plugins were successfully installed and activated.": ["Плагины были успешно установлены и активированы."], "MM/DD/YYYY": ["MM/DD/YYYY"], "Facebook for WooCommerce": ["Facebook для WooCommerce"], "Mailchimp for WooCommerce": ["Mailchimp for WooCommerce"], "Klarna Payments for WooCommerce": ["Klarna Payments для WooCommerce"], "Klarna Checkout for WooCommerce": ["<PERSON><PERSON>na Checkout для WooCommerce"], "Jetpack": ["Jetpack"]}}, "comment": {"reference": "assets/client/admin/data/index.js"}}