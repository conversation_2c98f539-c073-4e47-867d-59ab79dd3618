# Cookie Banner & Privacy Compliance for GDPR/CCPA/Google Consent Mode – Usercentrics Cookiebot #
* Contributors: cookiebot,phpgeekdk,aytac
* Tags: cookie banner, cookie consent, cookie notice, GDPR, privacy, cmp, consent‑management‑platform, google‑consent‑mode, compliance, gdpr‑compliance, ccpa, dma
* Requires at least: 4.4
* Tested up to: 6.8
* Stable tag: 4.5.7
* Requires PHP: 5.6
* License: GPLv2 or later

Install your cookie banner in minutes. Automatically scan and block cookies to comply with the GDPR, CCPA, Google Consent Mode v2. Free plan option.

## Description ##

Usercentrics Cookiebot CMP is a **Google‑certified** consent management platform (CMP) and cookie consent/banner solution for WordPress. It adds a **fully automated, highly customisable banner** that keeps you compliant with **GDPR, CCPA/CPRA, LGPD**, and other privacy regulations and frameworks while satisfying **Google Consent Mode v2** ad‑tech requirements. Seamlessly integrate with **Google Tag Manager** and Analytics to capture higher‑quality, consented data, automate consent signals, and deliver a transparent experience that boosts trust and conversions—no code required.

## Features ##

**⚡ Automatic cookie blocking**

Automatically block cookies and trackers until user consent is obtained, meeting data privacy requirements.

**🚀 Automated website scanning**

Regularly scan your website to detect and update cookies and tracking technologies in use to keep your consent banner and privacy policy page up to date.

**💫 Highly customizable**

Customize your WordPress cookie banner to match your site design and provide a user-friendly experience. Enable visitors to opt in or out of individual data processing services and trackers.

**✅ Integration with Google Consent Mode**

Keep running EU advertising campaigns with the streamlined Google Consent Mode integration. Collect valid user consent that’s signaled to Google Ads and Google Analytics to ensure continued conversion tracking and analytics optimization.

**🌎 Compliance with regulations and ad tech requirements**

Usercentrics Cookiebot WordPress Plugin enables you to comply with the following regulations and frameworks:

- GDPR (EU)
- DMA (EU)
- ePrivacy Directive (EU)
- TCF v2.2 (EU)
- LGPD (Brazil)
- POPIA (South Africa)
- CCPA/CPRA (California)
- VCDPA (Virginia)
- And more...

**📄 2,200+ legal templates**

Our database has over 2,200 legal templates for third-party Data Processing Services (DPS) ready to be used in your consent banner and save you time.

**🌟 Privacy Policy Embeddings**

Keep your privacy policy up to date with our privacy policy embeddings, enabling you to automatically display the information from our legal templates on your website.

**🇺🇸 CCPA and CPRA compliance setup**

To comply with the CCPA/CPRA, add a “Do Not Sell Or Share My Personal Information” link to the cookie policy declaration.

**📊 Comprehensive analytics dashboards and A/B testing**

Get actionable insights from detailed consent and user interaction analytics. Optimize consent data collection to increase user engagement and maintain personalized ad campaigns.

**🟠 Privacy Trigger**

Enable visitors to easily update and withdraw their consent at any given time with our user-friendly widget.

**📥 Proof of consent**

All consents are stored in a centralized log, accessible and downloadable for 12 months. Download specific consent histories with a single click for easy compliance reporting and data access requests.

**💬 Supports 60 languages**

Display your cookie banner and cookie policy in the preferred languages of users all over the world for clarity and better user experience.

**🔒 Secure data storage**

Securely store user consent data in our cloud-based environment on EU-based servers.

**Google-certified CMP**

Continue serving ads to users in the European Union, European Economic Area, or the UK with Usercentrics Cookiebot - assessed and certified by Google.

## Installation ##

Watch the video to learn how to install the Cookiebot CMP WordPress plugin. It’s easy! Or read our [step-by-step installation guide](https://support.cookiebot.com/hc/en-us/articles/************-Installing-Cookiebot-CMP-on-WordPress).

[youtube https://www.youtube.com/watch?v=JyugmNhPDq8]

Need assistance? Visit our [Help Center](https://support.usercentrics.com/hc/en-us) or [contact us](https://usercentricsforwordpress.zendesk.com/hc/en-us/requests/new).

## Support and updates ##

The Usercentrics Cookiebot WordPress Plugin is managed and regularly updated by our in-house Product team. We help you keep your business website compliant with relevant privacy regulations at all times and build trust with your site visitors.

With industry-leading scanning technology, the platform detects and controls all trackers and Data Processing Services (DPS) on your site automatically, speeding up implementation, enabling you to keep your consent banner up to date, and minimizing the risk of noncompliance.

Usercentrics Cookiebot CMP and the WordPress Plugin are designed to be used by both technical and non-technical teams that value plug-and-play solutions and reliable scalability. Transform data privacy compliance into a competitive advantage with Usercentrics Cookiebot.

If you have questions or need cookie banner support, visit our [Help Center](https://support.usercentrics.com/hc/en-us) or [contact us](https://usercentricsforwordpress.zendesk.com/hc/en-us/requests/new).

## Screenshots ##

1. Set up the Cookiebot CMP plugin using our easy-to-navigate dashboard
2. Automatically block cookies and trackers until user consent is given
3. Easily set a Google Tag Manager ID on your site
4. Activate Google Consent Mode to measure conversions and collect data
5. Enable the IAB TCF v2.0 on your website with a single click
6. Set up multiple configurations to enable compliance with two or more privacy regulations at once (e.g. GDPR and CCPA)
7. Visit our extensive Help Center or contact us for assistance

## Frequently Asked Questions ##

### Is Usercentrics Cookiebot free? ###
Yes, the cookie consent plugin is completely free to use up to a certain number of website visitors. Based on the amount of monthly visitors on your website and if you want to access premium features, your subscription may become subject to a monthly subscription fee.

See all details of Usercentrics CMP [plans and pricing](https://www.cookiebot.com/en/pricing/?utm_source=wordpress&utm_medium=referral&utm_campaign=banner).

### My banner is not visible. What should I do? ###

Once you’ve signed up or logged in, ensure the "Show banner on site" toggle is enabled (green).
If the toggle is enabled (green) and the banner is still not visible, [contact support](https://usercentricsforwordpress.zendesk.com/hc/en-us/requests/new).

### My scan has failed. What else can I do to rescan? ###
Log in to the Usercentrics Admin Interface using the same credentials as your WordPress plugin.
On the Companies Overview page, find your WordPress domain.
Click on your company and select your GDPR configuration.
In the left-hand menu, click on Service Settings.
Click the Start Scan button to initiate a new scan.

&nbsp;

* Domain Issue?
If the scan fails due to an invalid domain:
Click on Configuration in the left-hand menu.
Remove the incorrect domain and add a new one.

### I'm unable to upgrade? ###
Log in to the Usercentrics Admin Interface with your account credentials.
Click the Person Icon in the top-right corner.
Select Account & Billing.
Scroll to the Subscription section and click Manage Subscription.
Choose your desired plan and click Continue to Payment.
Enter billing details and click Buy Now.

### Unable to customize the banner? ###
Log in to the Usercentrics Admin Interface with your credentials.
In Companies Overview, find your WordPress domain and click on it.
Click on your GDPR configuration.
In the left-hand menu, click on Appearance.
Layout Tab: Adjust the layout of the first layer, second layer, and privacy trigger.
Styling Tab: Customize your banner design to match your brand.
Use the Preview Button to see your changes in real time.

### How to check your scan results or repeat a scan? ###
Log in to the Usercentrics Admin Interface with your credentials.
Click on your company and select your GDPR configuration.
In the left-hand menu, click on Service Settings.
View all data processing services detected in your website.
To repeat a scan, click Start Scan.

### Trial vs Free Plan - Why am I being offered a trial? ###
While a Free plan is available, we offer a 14-day premium trial so you can explore advanced features such as banner customization before choosing a plan. You can switch to a Free or Premium plan at any time during the trial. If you don’t select a plan before the 14 days end, your banner will be deactivated.

For pricing details, check our [pricing page](https://usercentrics.com/pricing/).

### How can I view the number of sessions? ###
Log in to the Usercentrics Admin Interface with your credentials..
Click the Person Icon in the top-right corner.
Select Account & Billing.
Scroll down to the Subscription section to see your session count.
Note: If you have multiple configurations, the session count includes all configurations, not just WordPress.

### Can I connect to an existing banner? ###
Yes, this is possible however, if you attempt to log in via the “Get Started” button, you will not be able to connect to an existing banner. Instead do the following:
Click on Go to Settings in the WordPress plugin footer.
On the Settings Page, find the Account ID field.
Enter either a Cookiebot Domain Group ID or a Usercentrics Configuration ID.
Click Connect Account.
Ensure the ID is valid to properly display the banner.
If using a Ruleset ID, ensure you select Compliance with multiple privacy laws (geolocation) in Your Current Account Setup.

### Can I switch to a different legal framework? ###
By default, a GDPR configuration is created upon sign-up. If you want to use a different legal framework (e.g., CCPA, VCDPA etc.) or multiple frameworks:
Click on Go to Settings in the WordPress plugin footer.
On the Settings Page, find the Account ID field.
Enter either a Cookiebot Domain Group ID or a Usercentrics Configuration ID.
Click Connect Account.
If using a Ruleset ID, ensure you select Compliance with multiple privacy laws (geolocation) in Your Current Account Setup.
If issues persist, [contact support](https://usercentricsforwordpress.zendesk.com/hc/en-us/requests/new)

### What is Google Consent Mode and can I use it with Usercentrics Cookiebot? ###
Google Consent Mode is an open API that enables your website to run all Google services, such as Google Analytics, Google Tag Manager, and Google Ads, based on the consent status of your end users. It helps you make the best use of visitors’ data and consent and automatically signals the consent status of your website users to all Google services.
Google Consent Mode is enabled by default in the Usercentrics Cookiebot plugin.

### Does Usercentrics Cookiebot integrate with WP Consent API? ###
Usercentrics Cookiebot is fully integrated with the WP Consent API. When your visitors give consent using Usercentrics Cookiebot, the consent will automatically be sent to the [WP Consent API](https://wordpress.org/plugins/wp-consent-api/).


## Changelog ##
**Cookiebot CMP Plugin will soon no longer support PHP 5. If your website still runs on this version we recommend upgrading so you can continue enjoying the features Cookiebot CMP offers.**

### 4.5.7 ###
Release date: May 22nd 2025

Cookiebot CMP version 4.5.7 is out! This release has some bugfixes

####Bugfixes####

* Fixed issues with user login sessions
* Resolved problems when disconnecting accounts or deactivating the plugin
* Prevented duplicate companies from being created
* Improved enforcement of user entitlements

### 4.5.6 ###
Release date: May 12th 2025

Cookiebot CMP version 4.5.6 is out! This release has some new features and bugfixes

####What's new####

* Updated plugin description and marketing information to better showcase our features and benefits

####Bugfixes####

* Resolved an issue where debug information was not showing for Cookiebot users on the support page.

### 4.5.5 ###
Release date: May 7th 2025

Cookiebot CMP version 4.5.5 is out! This release has some new features and bugfixes.

####What's new####

* Easier setup with clearer consent banner guidance
* Smoother banner reactivation process

####Bugfixes####

* Fixed support links
* Improved tooltip info in the dashboard
* Cleaned up leftover messages after disconnecting account
* Resolved issue with feedback form on deactivation

### 4.5.4 ###
Release date: April 29th 2025

Cookiebot CMP version 4.5.4 is out! This release has some new features and bugfixes.

####What's new####

* New page for free plan users
* Improve activation banner text
* Improve navigation menus (Support and Settings)

####Bugfixes####

* Fix disconnect flow for user created with the new onboarding process

### 4.5.3 ###
Release date: April 10th 2025

Cookiebot CMP version 4.5.3 is out! This release adds bugfixes.

####Bugfixes####

* Fix login redirection issue for logged-out user
* Fix default CMP configuration values
* Fix “Show Banner” settings for existing user accounts
* Fix links on Installed Wordpress plugins page
* Fix auto blocking mode
* Fix redirect expired sessions to "Get Started" page
* Fix free trial ended banner
* Clean-up user config data on plugin deactivation and deletion

### 4.5.2 ###
Release date: March 28th 2025

Cookiebot CMP version 4.5.2 is out! This release adds a bugfix.

####Bugfixes####
* Fix: remove auto-refresh of the scan process status

### 4.5.1 ###
Release date: March 27th 2025

Cookiebot CMP version 4.5.1 is out! This release adds a bugfix.

####Bugfixes####
* Fixed issue where the dashboard displayed the incorrect plan after upgrading to a paid subscription during a trial.

### 4.5.0 ###
Release date: March 27th 2025

Cookiebot CMP version 4.5.0 is out! This release has some new features.

####What's new####

* New onboarding experience: set up a fully functional GDPR-compliant cookie banner in just a few steps
* Automatic website scan: detects cookies and data processing services on your site right after account creation
* Auto-blocking enabled by default: services are automatically blocked until valid consent is obtained — no manual setup required
* Instant compliance: achieve GDPR compliance just by creating an account

### 4.4.2 ###
Release date: March 5th 2025

Cookiebot CMP version 4.4.2 is out! This release adds a bugfix.

####Bugfixes####
* Fix security issue - improve authorization check on survey submission Props: [Peter Thaleikis](https://peterthaleikis.com/)

### 4.4.1 ###
Release date: January 30th 2025

Cookiebot CMP version 4.4.1 is out! This release adds some minor content update.

####Other####
* Adds information CTAs for initial setup
* Updates wrong parameter sent on create account button

### 4.4.0 ###
Release date: January 28th 2025

Cookiebot CMP version 4.4.0 is out! This release adds a new navigability and some bugfixes.

####What's new####
* New navigability for legacy and new users

####Bugfixes####
* Fix content showing on options page before loading
* Fix GTM GCM script type on manual blocking

### 4.3.12 ###
Release date: December 2nd 2024

Cookiebot CMP version 4.3.12 is out! This release adds a bugfix and some minor content updates.

####Bugfixes####
* Fix notice appearing on textdomain early load

####Other####
* Adds implementation parameter to Cookiebot script
* Set WordPress tested up to version to 6.7.1
* Minor content updates

### 4.3.11 ###
Release date: November 20th 2024

Cookiebot CMP version 4.3.11 is out! This release adds a bugfix and some minor content updates.

####Bugfixes####
* Fix plugin content showing due to blocked resources

####Other####
* Set WordPress tested up to version to 6.7
* Add temporary content

### 4.3.10 ###
Release date: October 9th 2024

Cookiebot CMP version 4.3.10 is out! This release adds some improvements to the vendor selection and bugfix.

####What's new####
* Adds vendor select all and deselect all buttons for TCF and external vendors lists

####Bugfixes####
* Fix deliver of TCF version on script attribute

### ******* ###
Release date: September 12th 2024

Cookiebot CMP version ******* is out! This release updates some assets.

####Other####
* Set WordPress tested up to version to 6.6.2
* Assets update

### 4.3.9 ###
Release date: May 29th 2024

Cookiebot CMP version 4.3.9 is out! This release adds a bugfix and small features. Here is the complete list of this update

####Bugfixes####
* Fix deliver of blocking mode setting on multisite environment

####Other####
* Set WordPress tested up to version to 6.5.3
* Updates notices logic
* Readme update

### 4.3.8 ###
Release date: May 2nd 2024

This release updates readme content and WordPress supported version.

####Other####
* Set WordPress tested up to version to 6.5.2
* Readme update

### ******* ###
Release date: March 6th 2024

This release adds bugfixes. Here is the complete list of this update

####Bugfixes####
* Fix warning showing if purpose value is not of the right type

### 4.3.7 ###
Release date: March 6th 2024

Cookiebot CMP version 4.3.7 is out! This release adds bugfixes and small features. Here is the complete list of this update

####What's new####
* Set WordPress tested up to version to 6.4.3
* Add ignore parameter to inline scripts associated to ignored scripts Props: @jaakkolehtonen

####Bugfixes####
* Fix fatal error when no purposes added to restrictions

### 4.3.6 ###
Release date: February 28th 2024

Cookiebot CMP version 4.3.6 is out! This release adds small features to the plugin. Here is the complete list of this update

####Other####
* Add TCF script settings into the debug info
* Add message if IAB vendor list is offline
* Update translations

### 4.3.5 ###
Release date: February 14th 2024

Cookiebot CMP version 4.3.5 is out! This release adds custom settings to the TCF integration and removes the TCF version selector. Here is the complete list of this update

####What's new####
* Add TCF integration custom settings

####Other####
* Removes TCF version selector
* Update translations

### 4.3.4 ###
Release date: November 30th 2023

Cookiebot CMP version 4.3.4 is out! This release adds Google Consent Mode v2 support and updates content. Here is the complete list of this update

####What's new####
* Google Consent Mode v2 support

####Other####
* Removes temporal banner
* Update readme content

### 4.3.3 ###
Release date: November 15th 2023

Cookiebot CMP version 4.3.3 is out! This release updates content and adds some bugfixes. Here is the complete list of this update

####What's new####
* Set WordPress tested up to version to 6.4.1

####Bugfixes####
* Fix setting consent type on server side WP Consent API

####Other####
* Update script loading order
* Adds temporal banner
* Update readme content

### 4.3.2 ###
Release date: October 26th 2023

Cookiebot CMP version 4.3.2 is out! This release updates the readme content and adds some bugfixes. Here is the complete list of this update

####What's new####
* Addon recognition between WPForms lite and pro versions

####Bugfixes####
* Fix while creating a cookie with deny value for WP Consent API

####Other####
* Update readme content
* Update some CTA urls

### 4.3.1 ###
Release date: September 26th 2023

Cookiebot CMP version 4.3.1 is out! Here is the complete list of this update:

####Bugfixes####
* URL passthrough always enabled by default

### 4.3 ###
Release date: September 12th 2023

Cookiebot CMP version 4.3 is out! This release adds the new IAB TCF 2.2 framework support. Here is the complete list of this update:

####What's new####
* TCF 2.2 framework support and migration switch.
* New WP Consent API translations
* Set WordPress tested up to version to 6.3.1

####Other####
* Update debug info content
* PHP 5 support notice

### 4.2.14 ###
Release date: August 23th 2023

Cookiebot CMP version 4.2.14 is out! This release fixes an error when using WP Rocket and Facebook for Woocommerce with Cookiebot and adds some features. Here is the complete list of this update:

####What's new####
* WP Consent API cookie category mapping new UI
* Set banner and cookie declaration language using the website locale option
* Set WordPress tested up to version to 6.3

####Bugfixes####
* Fix fatal error when Facebook for Woocommerce addon is enabled and WP Rocket plugin is active.

####Other####
* Addons updates due to new plugins versions
* Translation updates
* Minor style changes

### 4.2.13 ###
Release date: August 7th 2023

Cookiebot CMP version 4.2.13 is out! This release fixes a bug on the ignored scripts feature and updates the content. Here is the complete list of this update:

####Bugfixes####
* Ignore scripts bug excluding script handlers

####Other####
* Content update

### 4.2.12 ###
Release date: July 4th 2023

Cookiebot CMP version 4.2.12 is out! This release updates Google Tag Manager and Google Consent Mode script implementation. Here is the complete list of this update:

####What's new####
* Allow select cookie categories for Google Tag Manager and Google Consent Mode on manual blocking mode
* Add new ES languages support

####Other####
* Add optional debug consent on deactivation survey

### 4.2.11 ###
Release date: June 7th 2023

Cookiebot CMP version 4.2.11 is out! This release fix a incompatibility issue with Slider Revolution plugin and updates Google Tag Manager and Google Consent Mode script implementation. Here is the complete list of this update:

####What's new####
* Update GTM and GCM script implementation
* Add exception to self-hosted audio files on Embed Autocorrect Addon

####Bugfixes####
* Incompatibility issue with Slider Revolution plugin
* Placeholder text out of place when Jetpack addon is active

####Other####
* Change scripts loading order
* Minor style changes

### 4.2.10 ###
Release date: May 25th 2023

Cookiebot CMP version 4.2.10 is out! Now our plugin supports the Virginia Consumer Data Protection Act (VCDPA) and you can add more banners settings on Multiple configurations tab. Here is the complete list of this update:

####What's new####
* New option to add more banners on Multiple configurations
* Added support for the Virginia Consumer Data Protection Act (VCDPA)
* Feedback survey
* Update compatibility with Official Meta Pixel Plugin

####Bugfixes####
* Tooltips blinking on cursor position

####Other####
* Change plugin admin urls argument build
* Change medium to referral on external links
* Minor style changes

### 4.2.9 - 2023-04-25 ###
* Code audit
* Update readme description
* Update templates links URLs Props: @chesio
* Update translations
* Fix multiple configuration bug
* Fix notice close link
* Fix french typo Props: @DevKylian

### 4.2.8 - 2023-04-05 ###
* Code audit
* Fix for WordPress 6.2 compatibility

### 4.2.7 - 2023-03-07 ###
* Update readme description
* Fix placeholder issue on custom content

### 4.2.6 - 2023-02-23 ###
* Fix review recommendation trigger

### 4.2.5 - 2023-02-23 ###
* Add translations Russian, Polish and Czech
* Update CMP on WP admin and URL passthrough disabled by default
* Update description readme.txt
* Update German, French, Spanish, Italian and Portuguese translations
* Update minor style changes
* Fix Network settings page only on network plugin activation

### 4.2.4 - 2023-01-23 ###
* Add review card on dashboard
* Add settings quick access link on plugins page list
* Add translations German, French, Spanish, Italian and Portuguese
* Update Auto blocking mode guide url
* Fix “Undefined index: tab” warning
* Fix Wrong label on Url passthrough input on first load
* Fix Menu links url on multisite

### 4.2.3 - 2022-12-21 ###
* Allow disabling URL passthrough when Google Consent Mode is activated

### 4.2.2 - 2022-12-05 ###
* Resources delivery on subfolders environments fix Props: [fritzprod](https://profiles.wordpress.org/fritzprod/)
* Fix security issue - remove possible vulnerable files Props: [ajaffri](https://profiles.wordpress.org/ajaffri/)

### 4.2.1 - 2022-12-02 ###
* PHP 5.6 compatibility fix
* Code formatting update

### 4.2.0 - 2022-11-28 ###
* Added new UI design
* Added multiple configurations
* Fixed PHP8 warnings when saving network settings.
* Activate Google Consent Mode by default

### 4.1.1 - 2022-07-01 ###
* Fixed undefined variable src when using instagram embed

### 4.1.0 - 2022-06-15 ###
* Added setting to ignore scripts from cookiebot scan
* Fixed PHP8 warnings
* Fixed attribute builder function for scripts
* Fixed blocking cookiebot script when editing theme

### 4.0.3 - 2022-02-23 ###
* Fixed wp-rocket not ignoring cookiebot script
* Fixed including gtm and gtc scripts when the setting was unchecked
* Updated addon to support latest version of CAOS

### 4.0.2 - 2022-01-20 ###
* Fixed missing dir path for require_once

### 4.0.1 - 2022-01-20 ###
* Fixed missing file

### 4.0.0 - 2022-01-20 ###
* Added support for SEOPress
* Updated code structure to improve maintainability
* Replaced filters & function names. Check [GitHub upgrade guide](https://github.com/CybotAS/CookiebotWP/blob/master/documentation/upgrade-guide.md) for more information about deprecations and breaking changes.

### 3.11.3 - 2021-11-25 ###
* Updated tests for add-to-any plugin version >= 1.8.2
* Added support for blocking the new script handles for add-to-any plugin version >= 1.8.2

### 3.11.2 - 2021-11-17 ###
* Updated CookieBot logo on settings page + network settings page

### 3.11.1 - 2021-09-22 ###
* Fixed unescaped PHP output
* Updated some translations

### 3.11.0 - 2021-07-16 ###
* Removed PHP-DI
* Fixed number of arguments in the settings-service-interface.php get_placeholder method signature
* Added custom container class to manage dependencies
* Added support for the Matomo Analytics plugin
# Added support for WP Google Analytics Events

### 3.10.1 - 2021-04-29 ###
* Added support for translating 'marketing', 'statistics', and 'preferences'
* Allow cookies for same domain embedded content

### 3.10.0 - 2021-03-22 ###
* Added support for translating the settings pages
* Added support for Enfold theme
* Added support for ExactMetrics
* Added support for Gutenberg Embed blocks
* Added support for newer version of Custom Facebook Feed
* Added support for newer version of Add To Any
* Fixed prior consent language bug
* Fixed embedding twitter
* Fixed multisite settings
* Prefixed the composer dependencies with Mozart

### 3.9.0 - 2020-10-20 ###
* Added support for Google Tag Manager and Google Consent Mode
* Added gtag TCF support
* Added WooCommerce Google Analytics Pro addon
* Support for enabling Cookiebot in administration

### 3.8.0 - 2020-09-07 ###
* New addon for Official Facebook Pixel plugin
* Fixes and improvements

### 3.7.1 - 2020-07-08 ###
* Fix "wp_enqueue_script was called incorrectly" notice

### 3.7.0 - 2020-07-06 ###
* Adding CCPA feature
* Adding Gutenberg Cookie Declaration block for editor

### 3.6.6 - 2020-06-16 ###
* Fix through addon for Lightspeed Cache
* Added addon for Enchanged Ecommerce for WooCommerce
* Added addon for Simple Share Buttons

### 3.6.5 - 2020-05-19 ###
* Adding fix for SG Optimizer
* Add support for latest version of Facebook for Woocommerce addon

### 3.6.3 - 2020-04-30 ###
* Adding support for default enabled addons
* Added filter tp addon list

### 3.6.2 - 2020-04-22 ###
* Adding WP Rocket addon
* Adding WP Mautic addon

### 3.6.1 - 2020-03-12 ###
* Fix security issue - possible XSS
* Update tests for addons

### 3.6.0 - 2020-02-18 ###
* Adding Debugging submenu
* Update GADWP addon to support newest version of plugin

### 3.5.0 - 2020-02-10 ###
* Adding integration with WP Consent API

### 3.4.2 - 2020-02-06 ###
* Fix for DIVI editor

### 3.4.1 - 2020-01-28 ###
* Removing manual fixes for Cookiebot when in auto
* Adjustments and updates to addons

### 3.4.0 - 2019-12-13 ###
* Removing Cookiebot when in auto mode and the user is logged in and has a edit themes capability
* Adding filter for regexp for fixing embedded media

### 3.3.0 - 2019-11-09 ###
* Fix for conflict with WPBakery Page Builder when Cookie blocking is in auto mode
* Fix for Elementor Extras causing JS errors in frontend when Cookie blocking is in auto mode
* Removing prepending of composer autoloader - causing conflicts with other plugins.

### 3.2.0 - 2019-10-29 ###
* Adding fix for conflict with Elementor Page Builder when Cookie blocking is in auto mode
* Adding fix for conflict with Divi Builder when Cookie blocking is in auto mode (still need to disable Cookiebot on admin pages to work properly).
* Minor adjustments to code style and unit tests

### 3.1.0 - 2019-09-24 ###
* Change option to hide cookie banner in WP Admin to instead disabling Cookiebot
* Set option to disable Cookiebot banner as default for new installs

### 3.0.1 - 2019-09-17 ###
* Clean up setting page
* Fixing failing addons after plugin updates

### 3.0.0 - 2019-09-10 ###
* Adding support for auto cookie blocking mode

### 2.5.0 - 2019-06-12 ###
* Add support for custom regex for embed autocorrect
* Adding Cookie Declaration widget
* Remove support for PHP 5.4

### 2.4.4 - 2019-05-22 ###
* Minor bugfixes in Embed Autocorrect addon

### 2.4.3 - 2019-05-16 ###
* Fix bug in Embed Autocorrect addon.

### 2.4.2 - 2019-05-15 ###
* Adding addthis addon
* Disable default autoupgrade
* Minor fixes

### 2.4.1 - 2019-03-19 ###
* Fix jetpack related warning

### 2.4.0 - 2019-03-19 ###
* Fixed bug resulting in some tags where not tagged
* Change Piwik addon to use output buffering
* Clean up redundant code in addons

### 2.3.0 - 2019-03-13 ###
* Added GADWP addon
* Changes in file structure of plugin

### 2.2.2 - 2019-02-12 ###
* Fix warning non-static call to get_cbid

### 2.2.1 - 2019-02-12 ###
* Adding support for WPForms
* Add plugin deactivation hook for addons

### 2.2.0 - 2019-02-11 ###
* Adding support for network wide settings on Multisite Wordpress setups.

### 2.1.5 - 2019-01-17 ###
* New addon: Custom Facebook Feed Pro
* Adding support for setting none, defer or async to Cookiebot script tags

### 2.1.3 - 2018-11-18 ###
* New addon: Popups by OptinMonster
* Added support for grouping addons for different versions of same plugin

### 2.1.2 - 2018-11-06 ###
* Auto correct addon added support for single quotes in iframe embeds

### 2.1.1 - 2018-11-02 ###
* Updated addons with new tests and better plugin integration

### 2.1.0 - 2018-10-05 ###
* Updated addons improved handling of tags
* Adding Basque as language option
* Remove .git files in addons

### 2.0.6 - 2018-09-26 ###
* Updated addons to support newest version of CAOS
* Minor bugfixes and text adjustments

### 2.0.5 - 2018-09-21 ###
* Added "Leave a review" admin notice

### 2.0.4 - 2018-09-18 ###
* Added [IAB Consent Framework](https://support.cookiebot.com/hc/en-us/articles/360007652694-Cookiebot-and-the-IAB-Consent-Framework) option
* Update Cookiebot Addons
* Minor bugfixes

### 2.0.3 - 2018-08-10 ###
* Removing informative message in admin_notice. Causing parse error for some users.

### 2.0.2 - 2018-08-01 ###
* Quickfix - disable Script Config. Made trouble for some users.

### 2.0.1 - 2018-08-01 ###
* Disable load of Addons if server runs PHP < 5.4.
* Fix placeholder in addons

### 2.0.0 - 2018-07-30 ###
* Merge Cookiebot and Cookiebot Addons plugins. Cookiebot plugin now bundled with addons making other plugins GDPR compliant.

### 1.6.2 - 2018-06-21 ###
* Fix check for WP Rocket.

### 1.6.1 - 2018-06-11 ###
* Fixing shortcode when using WP Rocket

### 1.6.0 - 2018-05-30 ###
* Support for removing cookie consent banner for tag manager users
* Support for multiple consent types in cookie_assist - eg. cookie_assist(['marketing','statistics']);
* Loading Cookiebot JS on admin pages too
* Adjusting help texts and adding links to demonstration videos.

### 1.5.1 - 2018-05-25 ###
* Adjusting readme.txt including new video

### 1.5.0 - 2018-05-22 ###
* Adding autoupdate for Cookiebot

### 1.4.2 - 2018-05-17 ###
* Fix undefined $lang bug in shortcode output

### 1.4.1 - 2018-05-11 ###
* Adjusting readme file

### 1.4.0 - 2018-05-10 ###
* Adding support for specifying language of cookie banner
* Adding optional "lang" attribute to [cookie_declaration] shortcode

### 1.3.0 - 2018-04-29 ###
* Bug fixed: Headers already sent when editing pages using shortcode

### 1.2.0 - 2018-03-28 ###
* Updating readme file with more details
* Adding cookiebot_active helper function

### 1.1.2 - 2018-02-27 ###
* Removing short array syntax to improve compatibility

### 1.1.1 - 2018-02-02 ###
* Adjusting readme.txt

### 1.1.0 - 2018-02-02 ###
* Adding data-culture to cookiebot script tags.

### 1.0.0 ###
* Initial release of the plugin.
