[24-May-2025 18:32:38 UTC] PHP Parse error:  Unmatched '}' in /Users/<USER>/ea/wp-content/plugins/fatal-plugin-auto-deactivator/fatal-plugin-auto-deactivator.php on line 21
[24-May-2025 18:32:38 UTC] Fatal Plugin Auto Deactivator: Auto-deactivated plugin: fatal-plugin-auto-deactivator/fatal-plugin-auto-deactivator.php due to fatal error in: /Users/<USER>/ea/wp-content/plugins/fatal-plugin-auto-deactivator/fatal-plugin-auto-deactivator.php
[24-May-2025 18:32:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fatal-plugin-auto-deactivator</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[24-May-2025 18:33:01 UTC] PHP Parse error:  Unmatched '}' in /Users/<USER>/ea/wp-content/plugins/fatal-plugin-auto-deactivator/fatal-plugin-auto-deactivator.php on line 21
[24-May-2025 18:33:17 UTC] PHP Parse error:  Unmatched '}' in /Users/<USER>/ea/wp-content/plugins/fatal-plugin-auto-deactivator/fatal-plugin-auto-deactivator.php on line 21
[24-May-2025 18:58:06 UTC] PHP Fatal error:  Uncaught Exception: Invalid post. in /Users/<USER>/ea/wp-content/plugins/elementor/core/settings/page/manager.php:101
Stack trace:
#0 /Users/<USER>/ea/wp-content/plugins/elementor/core/base/document.php(1798): Elementor\Core\Settings\Page\Manager->ajax_before_save_settings(Array, NULL)
#1 /Users/<USER>/ea/wp-content/plugins/elementor/core/base/document.php(916): Elementor\Core\Base\Document->save_settings(Array)
#2 /Users/<USER>/ea/wp-content/plugins/elementor/core/kits/manager.php(295): Elementor\Core\Base\Document->update_settings(Array)
#3 /Users/<USER>/ea/wp-content/plugins/elementor/core/kits/manager.php(464): Elementor\Core\Kits\Manager->update_kit_settings_based_on_option('site_name', 'Plugin Check')
#4 /Users/<USER>/ea/wp-includes/class-wp-hook.php(326): Elementor\Core\Kits\Manager->Elementor\Core\Kits\{closure}('My Site', 'Plugin Check')
#5 /Users/<USER>/ea/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#6 /Users/<USER>/ea/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#7 /Users/<USER>/ea/wp-includes/option.php(1020): do_action('update_option_b...', 'My Site', 'Plugin Check', 'blogname')
#8 /Users/<USER>/ea/wp-admin/includes/upgrade.php(82): update_option('blogname', 'Plugin Check')
#9 /Users/<USER>/ea/wp-content/plugins/plugin-check/includes/Checker/Runtime_Environment_Setup.php(267): wp_install('Plugin Check', 'plugincheck', 'demo@pluginchec...', false)
#10 /Users/<USER>/ea/wp-content/plugins/plugin-check/includes/Checker/Runtime_Environment_Setup.php(77): WordPress\Plugin_Check\Checker\Runtime_Environment_Setup->install_wordpress('https://ea.test', 'hello-elementor', Array)
#11 /Users/<USER>/ea/wp-content/plugins/plugin-check/includes/Admin/Admin_AJAX.php(134): WordPress\Plugin_Check\Checker\Runtime_Environment_Setup->set_up()
#12 /Users/<USER>/ea/wp-includes/class-wp-hook.php(324): WordPress\Plugin_Check\Admin\Admin_AJAX->set_up_environment('')
#13 /Users/<USER>/ea/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#14 /Users/<USER>/ea/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#15 /Users/<USER>/ea/wp-admin/admin-ajax.php(192): do_action('wp_ajax_plugin_...')
#16 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>/ea...')
#17 {main}
  thrown in /Users/<USER>/ea/wp-content/plugins/elementor/core/settings/page/manager.php on line 101
[25-May-2025 01:04:10 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 01:04:10 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 01:04:10 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 01:04:10 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 01:04:10 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 01:04:10 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 01:04:10 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 01:04:10 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 01:04:10 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 01:04:10 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:37:51 UTC] Automatic updates starting...
[25-May-2025 02:37:51 UTC]   Automatic plugin updates starting...
[25-May-2025 02:37:51 UTC]   Automatic plugin updates complete.
[25-May-2025 02:37:51 UTC]   Automatic theme updates starting...
[25-May-2025 02:37:51 UTC]   Automatic theme updates complete.
[25-May-2025 02:37:51 UTC] Automatic updates complete.
[25-May-2025 02:39:47 UTC] PHP Fatal error:  Uncaught Exception: Invalid post. in /Users/<USER>/ea/wp-content/plugins/elementor/core/settings/page/manager.php:101
Stack trace:
#0 /Users/<USER>/ea/wp-content/plugins/elementor/core/base/document.php(1798): Elementor\Core\Settings\Page\Manager->ajax_before_save_settings(Array, NULL)
#1 /Users/<USER>/ea/wp-content/plugins/elementor/core/base/document.php(916): Elementor\Core\Base\Document->save_settings(Array)
#2 /Users/<USER>/ea/wp-content/plugins/elementor/core/kits/manager.php(295): Elementor\Core\Base\Document->update_settings(Array)
#3 /Users/<USER>/ea/wp-content/plugins/elementor/core/kits/manager.php(464): Elementor\Core\Kits\Manager->update_kit_settings_based_on_option('site_name', 'Plugin Check')
#4 /Users/<USER>/ea/wp-includes/class-wp-hook.php(326): Elementor\Core\Kits\Manager->Elementor\Core\Kits\{closure}('My Site', 'Plugin Check')
#5 /Users/<USER>/ea/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#6 /Users/<USER>/ea/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#7 /Users/<USER>/ea/wp-includes/option.php(1020): do_action('update_option_b...', 'My Site', 'Plugin Check', 'blogname')
#8 /Users/<USER>/ea/wp-admin/includes/upgrade.php(82): update_option('blogname', 'Plugin Check')
#9 /Users/<USER>/ea/wp-content/plugins/plugin-check/includes/Checker/Runtime_Environment_Setup.php(267): wp_install('Plugin Check', 'plugincheck', 'demo@pluginchec...', false)
#10 /Users/<USER>/ea/wp-content/plugins/plugin-check/includes/Checker/Runtime_Environment_Setup.php(77): WordPress\Plugin_Check\Checker\Runtime_Environment_Setup->install_wordpress('https://ea.test', 'hello-elementor', Array)
#11 /Users/<USER>/ea/wp-content/plugins/plugin-check/includes/Admin/Admin_AJAX.php(134): WordPress\Plugin_Check\Checker\Runtime_Environment_Setup->set_up()
#12 /Users/<USER>/ea/wp-includes/class-wp-hook.php(324): WordPress\Plugin_Check\Admin\Admin_AJAX->set_up_environment('')
#13 /Users/<USER>/ea/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#14 /Users/<USER>/ea/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#15 /Users/<USER>/ea/wp-admin/admin-ajax.php(192): do_action('wp_ajax_plugin_...')
#16 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>/ea...')
#17 {main}
  thrown in /Users/<USER>/ea/wp-content/plugins/elementor/core/settings/page/manager.php on line 101
[25-May-2025 02:40:15 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:15 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:15 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:15 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:15 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:15 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:15 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:15 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:15 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:15 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:21 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:21 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:21 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:21 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:21 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:21 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:21 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:21 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:21 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 02:40:21 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:50:25 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:50:25 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:50:25 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:50:25 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:50:25 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:50:25 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:50:25 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:50:25 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:50:25 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:50:25 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:51:44 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:51:44 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:51:44 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:51:44 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:51:44 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:51:44 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:51:44 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:51:44 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:51:44 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:51:44 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "labels" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "view_item" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "labels" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "view_item" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "labels" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "view_item" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "labels" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "view_item" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "labels" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "view_item" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "labels" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "view_item" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "labels" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "view_item" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "labels" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "view_item" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "labels" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "view_item" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "labels" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "view_item" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "labels" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Warning:  Attempt to read property "view_item" on null in /Users/<USER>/ea/wp-admin/includes/class-wp-comments-list-table.php on line 1102
[25-May-2025 04:52:03 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:01:17 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:01:17 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:01:17 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:01:17 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:01:17 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:01:17 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:01:17 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:01:17 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:01:17 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:01:17 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:42 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:42 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:42 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:42 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:42 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:42 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:42 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:42 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:42 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:42 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:45 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:45 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:45 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:45 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:45 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:45 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:45 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:45 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:45 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:45 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:56 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:56 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:56 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:56 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:56 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:56 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:56 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:56 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:56 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:56 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:57 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:57 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:57 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:57 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:57 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:57 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:57 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:57 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:57 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 06:49:57 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:27 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:27 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:27 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:27 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:27 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:27 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:27 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:27 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:27 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:27 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:28 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:28 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:28 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:28 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:28 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:28 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:28 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:28 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>product</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:28 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
[25-May-2025 07:34:28 UTC] PHP Notice:  Function map_meta_cap was called <strong>incorrectly</strong>. The post type <code>shop_order</code> is not registered, so it may not be reliable to check the capability <code>edit_post</code> against a post of that type. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 4.4.0.) in /Users/<USER>/ea/wp-includes/functions.php on line 6121
